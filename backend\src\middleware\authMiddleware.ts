import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UnauthorizedError, ForbiddenError } from './errorHandler';

export interface AuthUser {
  id: string;
  email: string;
  userType: 'producer' | 'customer' | 'admin';
  status: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: AuthUser;
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user?: AuthUser;
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedError('Access token required');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    if (!token) {
      throw new UnauthorizedError('Access token required');
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET not configured');
    }

    const decoded = jwt.verify(token, jwtSecret) as any;
    
    // Add user info to request object
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      userType: decoded.userType,
      status: decoded.status
    };

    // Check if user is active
    if (req.user.status !== 'active') {
      throw new ForbiddenError('Account is not active');
    }

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new UnauthorizedError('Invalid token'));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new UnauthorizedError('Token expired'));
    } else {
      next(error);
    }
  }
};

// Role-based authorization middleware
export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new UnauthorizedError('Authentication required');
    }

    if (!allowedRoles.includes(req.user.userType)) {
      throw new ForbiddenError('Insufficient permissions');
    }

    next();
  };
};

// Admin only middleware
export const requireAdmin = requireRole(['ADMIN', 'admin']);

// Producer only middleware
export const requireProducer = requireRole(['PRODUCER', 'producer']);

// Customer only middleware
export const requireCustomer = requireRole(['CUSTOMER', 'customer']);

// Producer or Admin middleware
export const requireProducerOrAdmin = requireRole(['PRODUCER', 'producer', 'ADMIN', 'admin']);
