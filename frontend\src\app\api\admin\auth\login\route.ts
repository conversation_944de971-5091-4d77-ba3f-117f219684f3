import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // Basit admin kontrolü - gerçek uygulamada veritabanından kontrol edilecek
    if (email === '<EMAIL>' && password === 'Admin123456') {
      // Response oluştur
      const response = NextResponse.json({
        success: true,
        admin: {
          id: 'admin_1',
          email: '<EMAIL>',
          name: 'Admin User'
        }
      });

      // Cookie'yi response'a set et
      response.cookies.set('admin_token', 'admin_authenticated_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 gün
        path: '/'
      });

      return response;
    }

    return NextResponse.json(
      { error: 'Invalid admin credentials' },
      { status: 401 }
    );
  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json(
      { error: 'Login failed' },
      { status: 500 }
    );
  }
}
