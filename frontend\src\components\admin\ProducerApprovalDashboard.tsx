// RFC-501: Producer Approval Dashboard Component
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  FileText, 
  MapPin, 
  Building, 
  Phone, 
  Mail,
  Calendar,
  AlertTriangle
} from 'lucide-react';

interface ProducerRegistration {
  id: string;
  companyName: string;
  companyType: string;
  contactPerson: string;
  phone: string;
  countryCode: string;
  businessDescription: string;
  productionCapacity: number;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
  documents: DocumentVerification[];
  facilities: FacilityInspection[];
}

interface DocumentVerification {
  id: string;
  type: string;
  url: string;
  status: 'pending' | 'verified' | 'rejected';
  verifiedAt?: Date;
}

interface FacilityInspection {
  id: string;
  type: 'quarry' | 'factory';
  name: string;
  address: string;
  status: 'pending' | 'scheduled' | 'completed' | 'approved' | 'rejected';
  scheduledDate?: Date;
  inspectionDate?: Date;
}

export default function ProducerApprovalDashboard() {
  const [pendingApprovals, setPendingApprovals] = useState<ProducerRegistration[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<ProducerRegistration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // Fetch pending approvals from API
  useEffect(() => {
    fetchPendingApprovals();
  }, []);

  const fetchPendingApprovals = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/producers/pending', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add authorization header if needed
          // 'Authorization': `Bearer ${token}`
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setPendingApprovals(result.data || []);
        } else {
          console.error('Failed to fetch pending approvals:', result.message);
          setPendingApprovals([]);
        }
      } else {
        console.error('API request failed:', response.status, response.statusText);
        setPendingApprovals([]);
      }
    } catch (error) {
      console.error('Error fetching pending approvals:', error);
      setPendingApprovals([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (applicationId: string, notes: string) => {
    setActionLoading(true);
    try {
      // API call would go here
      console.log('Approving application:', applicationId, notes);
      
      // Update local state
      setPendingApprovals(prev => 
        prev.filter(app => app.id !== applicationId)
      );
      setSelectedApplication(null);
    } catch (error) {
      console.error('Error approving application:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async (applicationId: string, reason: string) => {
    setActionLoading(true);
    try {
      // API call would go here
      console.log('Rejecting application:', applicationId, reason);
      
      // Update local state
      setPendingApprovals(prev => 
        prev.filter(app => app.id !== applicationId)
      );
      setSelectedApplication(null);
    } catch (error) {
      console.error('Error rejecting application:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Beklemede</Badge>;
      case 'verified':
      case 'approved':
        return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>;
      case 'scheduled':
        return <Badge variant="outline"><Calendar className="w-3 h-3 mr-1" />Planlandı</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Üretici Onayları</h1>
          <p className="text-muted-foreground">
            Üretici kayıtlarını inceleyin ve onaylayın
          </p>
          <div className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
            💡 <strong>Kullanım:</strong> İncele butonuna tıklayarak başvuru detaylarını görüntüleyin, belgeleri kontrol edin ve karar verin.
          </div>
        </div>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {pendingApprovals.length} Beklemede
        </Badge>
      </div>

      {/* Applications List */}
      <div className="grid gap-4">
        {pendingApprovals.map((application) => (
          <Card key={application.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <Building className="w-5 h-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">{application.companyName}</h3>
                    {getStatusBadge(application.status)}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4" />
                      <span>{application.contactPerson}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>{application.countryCode}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>Applied: {application.submittedAt.toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Building className="w-4 h-4" />
                      <span>Capacity: {application.productionCapacity.toLocaleString()} m³/year</span>
                    </div>
                  </div>

                  <p className="text-sm">{application.businessDescription}</p>

                  {/* Document Status */}
                  <div className="flex flex-wrap gap-2 mt-3">
                    {application.documents.map((doc) => (
                      <div key={doc.id} className="flex items-center space-x-1">
                        <FileText className="w-3 h-3" />
                        <span className="text-xs">{doc.type.replace('_', ' ')}</span>
                        {getStatusBadge(doc.status)}
                      </div>
                    ))}
                  </div>
                </div>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={() => setSelectedApplication(application)}
                    >
                      İncele
                    </Button>
                  </DialogTrigger>
                  
                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Başvuru İnceleme: {application.companyName}</DialogTitle>
                    </DialogHeader>
                    
                    {selectedApplication && (
                      <ApplicationReviewModal 
                        application={selectedApplication}
                        onApprove={handleApprove}
                        onReject={handleReject}
                        isLoading={actionLoading}
                      />
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {pendingApprovals.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Hepsi Tamamlandı!</h3>
            <p className="text-muted-foreground">İncelenecek bekleyen üretici başvurusu yok.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Application Review Modal Component
function ApplicationReviewModal({ 
  application, 
  onApprove, 
  onReject, 
  isLoading 
}: {
  application: ProducerRegistration;
  onApprove: (id: string, notes: string) => void;
  onReject: (id: string, reason: string) => void;
  isLoading: boolean;
}) {
  const [notes, setNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');

  // Status badge function for modal
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Beklemede</Badge>;
      case 'verified':
      case 'approved':
        return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>;
      default:
        return <Badge variant="outline"><AlertTriangle className="w-3 h-3 mr-1" />Bilinmiyor</Badge>;
    }
  };

  return (
    <Tabs defaultValue="details" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="details" className="flex items-center space-x-2">
          <Building className="w-4 h-4" />
          <span>Şirket Detayları</span>
        </TabsTrigger>
        <TabsTrigger value="documents" className="flex items-center space-x-2">
          <FileText className="w-4 h-4" />
          <span>Belgeler ({application.documents.length})</span>
        </TabsTrigger>
        <TabsTrigger value="facilities" className="flex items-center space-x-2">
          <MapPin className="w-4 h-4" />
          <span>Tesisler ({application.facilities.length})</span>
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="details" className="space-y-4">
        <div className="space-y-6">
          {/* Temel Bilgiler */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-blue-900">Temel Şirket Bilgileri</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-blue-700">Şirket Adı</Label>
                <p className="font-medium text-lg">{application.companyName}</p>
              </div>
              <div>
                <Label className="text-blue-700">Şirket Türü</Label>
                <p className="font-medium">{application.companyType}</p>
              </div>
              <div>
                <Label className="text-blue-700">İletişim Kişisi</Label>
                <p className="font-medium">{application.contactPerson}</p>
              </div>
              <div>
                <Label className="text-blue-700">Telefon</Label>
                <p className="font-medium">{application.phone}</p>
              </div>
            </div>
          </div>

          {/* İş Açıklaması */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-green-900">İş Açıklaması</h3>
            <p className="font-medium text-green-800 leading-relaxed">{application.businessDescription}</p>
          </div>

          {/* Başvuru Bilgileri */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-gray-900">Başvuru Detayları</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-gray-700">Başvuru Tarihi</Label>
                <p className="font-medium">{new Date(application.submittedAt).toLocaleDateString('tr-TR')}</p>
              </div>
              <div>
                <Label className="text-gray-700">Ülke Kodu</Label>
                <p className="font-medium">{application.countryCode}</p>
              </div>
              <div>
                <Label className="text-gray-700">Üretim Kapasitesi</Label>
                <p className="font-medium">{application.productionCapacity} m²/ay</p>
              </div>
              <div>
                <Label className="text-gray-700">Mevcut Durum</Label>
                {getStatusBadge(application.status)}
              </div>
            </div>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="documents" className="space-y-4">
        {application.documents.map((doc) => (
          <div key={doc.id} className="flex items-center justify-between p-3 border rounded hover:bg-gray-50">
            <div className="flex items-center space-x-3 flex-1">
              <FileText className="w-5 h-5 text-blue-600" />
              <div className="flex-1">
                <p className="font-medium">{doc.type.replace('_', ' ')}</p>
                <a
                  href={doc.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 underline"
                >
                  Belgeyi Görüntüle
                </a>
                <p className="text-xs text-muted-foreground mt-1">
                  {doc.verifiedAt ? `Doğrulandı: ${new Date(doc.verifiedAt).toLocaleDateString('tr-TR')}` : 'Henüz doğrulanmadı'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(doc.status)}
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  // Belge durumunu değiştir
                  console.log('Belge durumu değiştir:', doc.id);
                }}
              >
                Durum Değiştir
              </Button>
            </div>
          </div>
        ))}
        {application.documents.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Henüz belge yüklenmemiş</p>
          </div>
        )}
      </TabsContent>
      
      <TabsContent value="facilities" className="space-y-4">
        {application.facilities.map((facility) => (
          <div key={facility.id} className="flex items-center justify-between p-4 border rounded hover:bg-gray-50">
            <div className="flex items-center space-x-3 flex-1">
              <MapPin className="w-5 h-5 text-green-600" />
              <div className="flex-1">
                <p className="font-medium text-lg">{facility.name}</p>
                <p className="text-sm text-muted-foreground">{facility.address}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    Tür: {facility.type === 'quarry' ? 'Ocak' : 'Fabrika'}
                  </span>
                  {facility.inspectionDate && (
                    <span className="text-xs text-muted-foreground">
                      Denetim: {new Date(facility.inspectionDate).toLocaleDateString('tr-TR')}
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(facility.status)}
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  // Tesis denetimi planla
                  console.log('Tesis denetimi planla:', facility.id);
                }}
              >
                Denetim Planla
              </Button>
            </div>
          </div>
        ))}
        {application.facilities.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Henüz tesis bilgisi eklenmemiş</p>
          </div>
        )}
      </TabsContent>

      {/* İnceleme Özeti */}
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h3 className="font-semibold text-lg mb-3 text-yellow-900">İnceleme Özeti</h3>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <p className="font-medium text-yellow-700">Toplam Belge</p>
            <p className="text-2xl font-bold text-yellow-900">{application.documents.length}</p>
          </div>
          <div className="text-center">
            <p className="font-medium text-yellow-700">Onaylanan Belge</p>
            <p className="text-2xl font-bold text-green-600">
              {application.documents.filter(doc => doc.status === 'verified').length}
            </p>
          </div>
          <div className="text-center">
            <p className="font-medium text-yellow-700">Toplam Tesis</p>
            <p className="text-2xl font-bold text-yellow-900">{application.facilities.length}</p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white p-6 border-t-2 border-gray-200">
        <div className="space-y-4">
          <div>
            <Label htmlFor="notes" className="text-lg font-semibold">Onay Notları</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Onay için notlarınızı, koşullarınızı veya özel talimatlarınızı ekleyin..."
              rows={4}
              className="mt-2"
            />
          </div>

          <div className="flex space-x-4">
            <Button
              onClick={() => onApprove(application.id, notes)}
              disabled={isLoading}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 text-lg"
            >
              <CheckCircle className="w-5 h-5 mr-2" />
              {isLoading ? 'Onaylanıyor...' : 'Başvuruyu Onayla'}
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="destructive"
                  disabled={isLoading}
                  className="flex-1 py-3 text-lg"
                >
                  <XCircle className="w-5 h-5 mr-2" />
                  Başvuruyu Reddet
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-red-700">Başvuruyu Reddet</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="bg-red-50 p-3 rounded border border-red-200">
                    <p className="text-sm text-red-700">
                      ⚠️ Bu işlem geri alınamaz. Başvuru reddedildikten sonra üretici bilgilendirilecektir.
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="reason" className="font-semibold">Ret Sebebi *</Label>
                    <Textarea
                      id="reason"
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      placeholder="Başvurunun neden reddedildiğini detaylı olarak açıklayın. Bu bilgi üreticiye iletilecektir..."
                      rows={5}
                      className="mt-2"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Minimum 20 karakter gerekli ({rejectionReason.length}/20)
                    </p>
                  </div>

                  <div className="flex space-x-2">
                    <DialogTrigger asChild>
                      <Button variant="outline" className="flex-1">
                        İptal
                      </Button>
                    </DialogTrigger>
                    <Button
                      onClick={() => onReject(application.id, rejectionReason)}
                      disabled={isLoading || rejectionReason.trim().length < 20}
                      variant="destructive"
                      className="flex-1"
                    >
                      {isLoading ? 'Reddediliyor...' : 'Reddet'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </Tabs>
  );
}
