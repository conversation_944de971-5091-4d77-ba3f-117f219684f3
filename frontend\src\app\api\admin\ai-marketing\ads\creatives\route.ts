import { NextRequest, NextResponse } from 'next/server';

// Creative API endpoints for AI Marketing Ads

interface AdCreative {
  id: string;
  campaignId: string;
  type: 'text' | 'image' | 'video' | 'carousel';
  headline: string;
  description: string;
  imageUrl?: string;
  performance: {
    impressions: number;
    clicks: number;
    ctr: number;
  };
  aiGenerated: boolean;
}

// Mock creatives storage (gerçek implementasyonda database kullanılacak)
let creatives: AdCreative[] = [
  {
    id: '1',
    campaignId: '1',
    type: 'text',
    headline: 'Premium Traverten Ürünleri',
    description: 'Türkiye\'nin en kaliteli traverten ürünleri ile projelerinizi tamamlayın.',
    performance: {
      impressions: 15230,
      clicks: 405,
      ctr: 2.66
    },
    aiGenerated: true
  },
  {
    id: '2',
    campaignId: '2',
    type: 'image',
    headline: 'Lüks Mermer Koleksiyonu',
    description: 'Evinizi saray haline getirin. Premium mermer ürünlerimizi keşfedin.',
    imageUrl: '/images/marble-collection.jpg',
    performance: {
      impressions: 29450,
      clicks: 834,
      ctr: 2.83
    },
    aiGenerated: true
  }
];

// GET - Fetch all creatives
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const campaignId = url.searchParams.get('campaignId');

    let filteredCreatives = creatives;
    if (campaignId) {
      filteredCreatives = creatives.filter(c => c.campaignId === campaignId);
    }

    return NextResponse.json({
      success: true,
      data: filteredCreatives,
      message: 'Creatives fetched successfully'
    });
  } catch (error) {
    console.error('Error fetching creatives:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch creatives' },
      { status: 500 }
    );
  }
}

// POST - Create new creative
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newCreative: AdCreative = {
      id: Date.now().toString(),
      campaignId: body.campaignId,
      type: body.type,
      headline: body.headline,
      description: body.description,
      imageUrl: body.imageUrl,
      performance: {
        impressions: 0,
        clicks: 0,
        ctr: 0
      },
      aiGenerated: body.aiGenerated || false
    };

    creatives.push(newCreative);

    return NextResponse.json({
      success: true,
      data: newCreative,
      message: 'Creative created successfully'
    });
  } catch (error) {
    console.error('Error creating creative:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create creative' },
      { status: 500 }
    );
  }
}
