import { NextRequest, NextResponse } from 'next/server';

// AI Settings API endpoints for AI Marketing

interface AISettings {
  orchestrator: {
    cycleInterval: number;
    maxRetries: number;
    isActive: boolean;
  };
  apiKeys: {
    openai: string;
    claude: string;
    gemini: string;
  };
  emailMarketing: {
    maxCampaignsPerDay: number;
    defaultFrequency: 'daily' | 'weekly' | 'monthly';
    autoApproval: boolean;
    contentTone: string;
  };
  socialMedia: {
    maxPostsPerDay: number;
    autoApproval: boolean;
    platforms: {
      facebook: boolean;
      instagram: boolean;
      linkedin: boolean;
      twitter: boolean;
      youtube: boolean;
      tiktok: boolean;
    };
    contentTone: string;
  };
  customerAcquisition: {
    maxContactsPerDay: number;
    responseTimeout: number;
    autoFollowUp: boolean;
    leadScoreThreshold: number;
  };
  adsManagement: {
    maxBudgetPerDay: number;
    autoOptimization: boolean;
    bidStrategy: 'conservative' | 'balanced' | 'aggressive';
    roasThreshold: number;
  };
}

// Mock settings storage (gerçek implementasyonda database kullanılacak)
let currentSettings: AISettings = {
  orchestrator: {
    cycleInterval: 30,
    maxRetries: 3,
    isActive: true
  },
  apiKeys: {
    openai: 'sk-***************************',
    claude: 'claude-*********************',
    gemini: 'gemini-*********************'
  },
  emailMarketing: {
    maxCampaignsPerDay: 5,
    defaultFrequency: 'weekly',
    autoApproval: false,
    contentTone: 'professional'
  },
  socialMedia: {
    maxPostsPerDay: 10,
    autoApproval: false,
    platforms: {
      facebook: true,
      instagram: true,
      linkedin: true,
      twitter: true,
      youtube: false,
      tiktok: false
    },
    contentTone: 'friendly'
  },
  customerAcquisition: {
    maxContactsPerDay: 20,
    responseTimeout: 72,
    autoFollowUp: true,
    leadScoreThreshold: 75
  },
  adsManagement: {
    maxBudgetPerDay: 500,
    autoOptimization: true,
    bidStrategy: 'balanced',
    roasThreshold: 2.0
  }
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');

    if (section && section in currentSettings) {
      return NextResponse.json({ 
        success: true, 
        data: { [section]: currentSettings[section as keyof AISettings] }
      });
    }

    return NextResponse.json({ success: true, data: currentSettings });
  } catch (error) {
    console.error('Settings API GET error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'save':
        return saveSettings(body.settings);
      case 'reset':
        return resetSettings();
      case 'validate':
        return validateSettings(body.settings);
      case 'test-api-keys':
        return testApiKeys(body.apiKeys);
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Settings API POST error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const settings = await request.json();
    return saveSettings(settings);
  } catch (error) {
    console.error('Settings API PUT error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Save settings
async function saveSettings(newSettings: AISettings) {
  try {
    // Validate settings
    const validation = validateSettingsData(newSettings);
    if (!validation.isValid) {
      return NextResponse.json({ 
        success: false, 
        error: 'Validation failed',
        details: validation.errors 
      }, { status: 400 });
    }

    // Simulate database save
    console.log('Saving AI settings:', newSettings);
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Update current settings
    currentSettings = { ...newSettings };

    // Log the change
    console.log('AI settings updated successfully');

    return NextResponse.json({ 
      success: true, 
      message: 'Ayarlar başarıyla kaydedildi',
      data: currentSettings,
      savedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error saving settings:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Ayarlar kaydedilemedi' 
    }, { status: 500 });
  }
}

// Reset settings to defaults
async function resetSettings() {
  try {
    console.log('Resetting AI settings to defaults');
    
    // Simulate database reset
    await new Promise(resolve => setTimeout(resolve, 500));

    // Reset to default values
    currentSettings = {
      orchestrator: {
        cycleInterval: 30,
        maxRetries: 3,
        isActive: true
      },
      apiKeys: {
        openai: '',
        claude: '',
        gemini: ''
      },
      emailMarketing: {
        maxCampaignsPerDay: 5,
        defaultFrequency: 'weekly',
        autoApproval: false,
        contentTone: 'professional'
      },
      socialMedia: {
        maxPostsPerDay: 10,
        autoApproval: false,
        platforms: {
          facebook: true,
          instagram: true,
          linkedin: false,
          twitter: false,
          youtube: false,
          tiktok: false
        },
        contentTone: 'friendly'
      },
      customerAcquisition: {
        maxContactsPerDay: 20,
        responseTimeout: 72,
        autoFollowUp: true,
        leadScoreThreshold: 75
      },
      adsManagement: {
        maxBudgetPerDay: 100,
        autoOptimization: false,
        bidStrategy: 'conservative',
        roasThreshold: 2.0
      }
    };

    return NextResponse.json({ 
      success: true, 
      message: 'Ayarlar varsayılan değerlere sıfırlandı',
      data: currentSettings 
    });
  } catch (error) {
    console.error('Error resetting settings:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Ayarlar sıfırlanamadı' 
    }, { status: 500 });
  }
}

// Validate settings
async function validateSettings(settings: AISettings) {
  const validation = validateSettingsData(settings);
  
  return NextResponse.json({ 
    success: true, 
    data: validation 
  });
}

// Test API keys
async function testApiKeys(apiKeys: any) {
  const results = {
    openai: false,
    claude: false,
    gemini: false
  };

  try {
    // Mock API key testing
    console.log('Testing API keys...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate test results
    if (apiKeys.openai && apiKeys.openai.startsWith('sk-')) {
      results.openai = true;
    }
    if (apiKeys.claude && apiKeys.claude.startsWith('claude-')) {
      results.claude = true;
    }
    if (apiKeys.gemini && apiKeys.gemini.startsWith('gemini-')) {
      results.gemini = true;
    }

    return NextResponse.json({ 
      success: true, 
      message: 'API anahtarları test edildi',
      data: results 
    });
  } catch (error) {
    console.error('Error testing API keys:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'API anahtarları test edilemedi' 
    }, { status: 500 });
  }
}

// Helper function to validate settings data
function validateSettingsData(settings: AISettings) {
  const errors: string[] = [];

  // Orchestrator validation
  if (settings.orchestrator.cycleInterval < 1) {
    errors.push('Döngü aralığı en az 1 dakika olmalıdır');
  }
  if (settings.orchestrator.maxRetries < 1) {
    errors.push('Maksimum deneme sayısı en az 1 olmalıdır');
  }

  // Email Marketing validation
  if (settings.emailMarketing.maxCampaignsPerDay < 1) {
    errors.push('Günlük maksimum kampanya sayısı en az 1 olmalıdır');
  }
  if (settings.emailMarketing.maxCampaignsPerDay > 50) {
    errors.push('Günlük maksimum kampanya sayısı 50\'yi geçemez');
  }

  // Social Media validation
  if (settings.socialMedia.maxPostsPerDay < 1) {
    errors.push('Günlük maksimum post sayısı en az 1 olmalıdır');
  }
  if (settings.socialMedia.maxPostsPerDay > 100) {
    errors.push('Günlük maksimum post sayısı 100\'ü geçemez');
  }

  // Customer Acquisition validation
  if (settings.customerAcquisition.maxContactsPerDay < 1) {
    errors.push('Günlük maksimum iletişim sayısı en az 1 olmalıdır');
  }
  if (settings.customerAcquisition.responseTimeout < 1) {
    errors.push('Yanıt zaman aşımı en az 1 saat olmalıdır');
  }
  if (settings.customerAcquisition.leadScoreThreshold < 0 || settings.customerAcquisition.leadScoreThreshold > 100) {
    errors.push('Lead skor eşiği 0-100 arasında olmalıdır');
  }

  // Ads Management validation
  if (settings.adsManagement.maxBudgetPerDay < 1) {
    errors.push('Günlük maksimum bütçe en az 1 TL olmalıdır');
  }
  if (settings.adsManagement.roasThreshold < 0.1) {
    errors.push('ROAS eşiği en az 0.1 olmalıdır');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
