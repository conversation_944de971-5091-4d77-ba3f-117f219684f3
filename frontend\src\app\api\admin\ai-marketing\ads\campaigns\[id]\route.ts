import { NextRequest, NextResponse } from 'next/server';

// Individual Campaign API endpoints

interface AdCampaign {
  id: string;
  name: string;
  platform: 'google_ads' | 'facebook_ads' | 'linkedin_ads' | 'twitter_ads';
  campaignType: 'search' | 'display' | 'video' | 'shopping' | 'social';
  status: 'active' | 'paused' | 'ended' | 'draft';
  budget: {
    total: number;
    daily: number;
    spent: number;
    currency: string;
  };
  performance: {
    impressions: number;
    clicks: number;
    conversions: number;
    ctr: number;
    cpc: number;
    cpa: number;
    roas: number;
  };
  targetAudience: {
    locations: string[];
    demographics: string;
    interests: string[];
  };
  startDate: Date;
  endDate?: Date;
  aiManaged: boolean;
  lastOptimized?: Date;
}

// Mock campaigns storage (gerçek implementasyonda database kullanılacak)
let campaigns: AdCampaign[] = [
  {
    id: '1',
    name: 'Traverten Ürünleri - Google Search',
    platform: 'google_ads',
    campaignType: 'search',
    status: 'active',
    budget: {
      total: 5000,
      daily: 100,
      spent: 2340,
      currency: 'USD'
    },
    performance: {
      impressions: 45230,
      clicks: 1205,
      conversions: 34,
      ctr: 2.66,
      cpc: 1.94,
      cpa: 68.82,
      roas: 3.2
    },
    targetAudience: {
      locations: ['US', 'DE', 'IT'],
      demographics: '25-65, All genders',
      interests: ['Construction', 'Architecture', 'Home Improvement']
    },
    startDate: new Date('2025-06-15'),
    endDate: new Date('2025-07-15'),
    aiManaged: true,
    lastOptimized: new Date('2025-07-03')
  }
];

// GET - Fetch specific campaign
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const campaign = campaigns.find(c => c.id === params.id);
    
    if (!campaign) {
      return NextResponse.json(
        { success: false, error: 'Campaign not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: campaign,
      message: 'Campaign fetched successfully'
    });
  } catch (error) {
    console.error('Error fetching campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch campaign' },
      { status: 500 }
    );
  }
}

// PUT - Update campaign
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const campaignIndex = campaigns.findIndex(c => c.id === params.id);
    
    if (campaignIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Campaign not found' },
        { status: 404 }
      );
    }

    // Update campaign
    campaigns[campaignIndex] = {
      ...campaigns[campaignIndex],
      ...body,
      lastOptimized: new Date()
    };

    return NextResponse.json({
      success: true,
      data: campaigns[campaignIndex],
      message: 'Campaign updated successfully'
    });
  } catch (error) {
    console.error('Error updating campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update campaign' },
      { status: 500 }
    );
  }
}

// PATCH - Update campaign status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const campaignIndex = campaigns.findIndex(c => c.id === params.id);
    
    if (campaignIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Campaign not found' },
        { status: 404 }
      );
    }

    // Update only status
    campaigns[campaignIndex].status = body.status;
    campaigns[campaignIndex].lastOptimized = new Date();

    return NextResponse.json({
      success: true,
      data: campaigns[campaignIndex],
      message: 'Campaign status updated successfully'
    });
  } catch (error) {
    console.error('Error updating campaign status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update campaign status' },
      { status: 500 }
    );
  }
}

// DELETE - Delete campaign
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const campaignIndex = campaigns.findIndex(c => c.id === params.id);
    
    if (campaignIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Campaign not found' },
        { status: 404 }
      );
    }

    campaigns.splice(campaignIndex, 1);

    return NextResponse.json({
      success: true,
      message: 'Campaign deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete campaign' },
      { status: 500 }
    );
  }
}
