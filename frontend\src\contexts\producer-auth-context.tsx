'use client'

import * as React from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ProducerRegistrationModal } from '@/components/ui/producer-registration-modal'

// Producer types
interface Producer {
  id: string
  name: string
  email: string
  companyName: string
  role: 'producer'
  isApproved: boolean
  hasQuarry: boolean // Ocak sahibi mi?
  phone?: string
  address?: string
}

interface ProducerAuthContextType {
  isAuthenticated: boolean
  producer: Producer | null
  isLoading: boolean
  
  // Modal states
  isLoginModalOpen: boolean
  isRegisterModalOpen: boolean
  
  // Actions
  login: (email: string, password: string) => Promise<boolean>
  register: (data: ProducerRegisterData) => Promise<boolean>
  logout: () => void
  
  // Modal controls
  showLoginModal: () => void
  showRegisterModal: () => void
  hideModals: () => void
}

interface ProducerRegisterData {
  // Ki<PERSON>isel Bilgiler
  name: string
  email: string
  password: string
  confirmPassword: string
  phone: string
  position: string

  // Şirket Bilgileri
  companyName: string
  taxNumber: string
  tradeRegistryNumber: string
  companyAddress: string
  companyPhone: string
  companyEmail: string
  website?: string
  foundedYear: string
  employeeCount: string

  // Üretim Bilgileri
  productionCapacity: string
  productCategories: string[]
  certifications: string[]
  customCertification?: string

  // Lokasyonlar
  hasQuarry: boolean
  quarries: Array<{
    name: string
    address: string
    googleMapsLink?: string
    description?: string
  }>
  factories: Array<{
    name: string
    address: string
    googleMapsLink?: string
    capacity: string
    description?: string
  }>

  // Üretim Kapasite Raporu
  productionCapacityReport?: File

  // Fason Üretim
  providesCustomManufacturing: boolean
  customManufacturingDetails?: string

  // Belgeler
  documents: Array<{
    type: string
    file?: File
    fileName?: string
    uploaded: boolean
  }>

  // Şirket Tanıtımı
  companyDescription: string
}

const ProducerAuthContext = React.createContext<ProducerAuthContextType | undefined>(undefined)

interface ProducerAuthProviderProps {
  children: React.ReactNode
}

export function ProducerAuthProvider({ children }: ProducerAuthProviderProps) {
  const router = useRouter()
  const [producer, setProducer] = React.useState<Producer | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)
  const [isLoginModalOpen, setIsLoginModalOpen] = React.useState(false)
  const [isRegisterModalOpen, setIsRegisterModalOpen] = React.useState(false)
  const [mounted, setMounted] = React.useState(false)

  const isAuthenticated = !!producer

  // Initialize auth state
  React.useEffect(() => {
    const initAuth = () => {
      try {
        setMounted(true)
        console.log('ProducerAuth: Initializing auth state...')
        const storedProducer = localStorage.getItem('producer')
        console.log('ProducerAuth: Stored producer:', storedProducer)

        if (storedProducer && storedProducer !== 'null') {
          const producer = JSON.parse(storedProducer)
          console.log('ProducerAuth: Parsed producer:', producer)
          setProducer(producer)
          console.log('ProducerAuth: Producer set successfully')
        } else {
          console.log('ProducerAuth: No stored producer found')
          // Clear any invalid data
          localStorage.removeItem('producer')
          setProducer(null)
        }
      } catch (error) {
        console.error('Error loading producer auth state:', error)
        localStorage.removeItem('producer')
        // Remove cookie
        document.cookie = 'producer=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      } finally {
        setIsLoading(false)
        console.log('ProducerAuth: Loading complete')
      }
    }

    initAuth()
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      console.log('Attempting login with:', email)

      // API call to login (backend port 8000)
      const response = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Cookie desteği için
        body: JSON.stringify({ email, password, userType: 'producer' })
      })

      const result = await response.json()
      console.log('Login response:', result)

      if (result.success) {
        // Kullanıcı tipini kontrol et - backend'de yapılıyor
        console.log('Producer login successful, full user data:', result.data.user);
        console.log('Producer login successful, user type:', result.data.user.userType);

        // Backend'den gelen veriyi kullanarak producer objesi oluştur
        const producer: Producer = {
          id: result.data.user.id,
          name: result.data.user.name || result.data.user.firstName || 'İsimsiz Üretici',
          email: result.data.user.email,
          companyName: result.data.user.company || result.data.user.companyName || 'Şirket Adı',
          role: 'producer',
          isApproved: result.data.user.status === 'ACTIVE',
          hasQuarry: result.data.user.profile?.hasQuarry || false,
          phone: result.data.user.phone || result.data.user.profile?.phone
        }

        setProducer(producer)
        localStorage.setItem('producer', JSON.stringify(producer))
        document.cookie = `producer=${encodeURIComponent(JSON.stringify(producer))}; path=/; max-age=${7 * 24 * 60 * 60}`
        setIsLoginModalOpen(false)

        console.log('Producer login successful, redirecting to dashboard...')
        console.log('Router object:', router)

        // Yönlendirme işlemini debug edelim
        try {
          await router.push('/producer/dashboard')
          console.log('Redirect successful!')
        } catch (error) {
          console.error('Redirect failed:', error)
        }

        return true
      } else {
        // Admin onayı bekleniyor mesajı
        if (result.requiresApproval) {
          alert('Hesabınız henüz admin onayında. Onay sonrası giriş yapabileceksiniz.')
        } else {
          const errorMessage = result.error || 'Bilinmeyen hata';
          alert('Giriş başarısız: ' + errorMessage);
        }
        return false
      }
    } catch (error) {
      console.error('Login error:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (data: ProducerRegisterData): Promise<boolean> => {
    try {
      setIsLoading(true)



      // API call to register producer (backend port 8000)
      const response = await fetch('http://localhost:8000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Cookie desteği için
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          userType: 'producer',
          companyName: data.companyName,
          countryCode: 'TR',
          contactPerson: data.name,
          phone: data.phone,
          // Ek bilgiler
          taxNumber: data.taxNumber,
          tradeRegistryNumber: data.tradeRegistryNumber,
          companyAddress: data.companyAddress,
          companyPhone: data.companyPhone,
          companyEmail: data.companyEmail,
          website: data.website,
          foundedYear: data.foundedYear,
          employeeCount: data.employeeCount,
          productionCapacity: data.productionCapacity,
          productCategories: data.productCategories,
          certifications: data.certifications,
          hasQuarry: data.hasQuarry,
          quarries: data.quarries,
          factories: data.factories,
          providesCustomManufacturing: data.providesCustomManufacturing,
          customManufacturingDetails: data.customManufacturingDetails,
          companyDescription: data.companyDescription
        })
      })

      const result = await response.json()

      // Detaylı hata logları
      if (!result.success) {
        console.log('=== REGISTRATION ERROR ===');
        console.log('Response status:', response.status);
        console.log('Error result:', result);
        if (result.details) {
          console.log('Validation details:', result.details);
        }
      }

      if (result.success) {
        setIsRegisterModalOpen(false)

        // Kayıt başarılı mesajı göster
        if (result.data.requiresApproval) {
          alert('Kayıt başarılı! Üretici hesabınız admin onayına gönderildi. Onay sonrası email ile bilgilendirileceksiniz.')
        } else {
          alert('Kayıt başarılı! Lütfen giriş yapın.')
        }

        // Kayıt başarılı olduğunda giriş formuna yönlendir
        setIsRegisterModalOpen(false)
        setIsLoginModalOpen(true)

        return true
      } else {
        console.error('Registration failed:', result.error)

        // Kullanıcıya daha detaylı hata mesajı göster
        let errorMessage = 'Kayıt işlemi başarısız oldu';
        if (result.message) {
          errorMessage = result.message;
        } else if (result.details && result.details.length > 0) {
          errorMessage = result.details.map((detail: any) => detail.msg).join(', ');
        } else if (result.error) {
          errorMessage = result.error;
        }

        alert(errorMessage);
        return false
      }
    } catch (error) {
      console.error('Registration error:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setProducer(null)
    localStorage.removeItem('producer')
    // Remove cookie
    document.cookie = 'producer=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    router.push('/')
  }

  const showLoginModal = () => {
    setIsLoginModalOpen(true)
    setIsRegisterModalOpen(false)
  }

  const showRegisterModal = () => {
    setIsRegisterModalOpen(true)
    setIsLoginModalOpen(false)
  }

  const hideModals = () => {
    setIsLoginModalOpen(false)
    setIsRegisterModalOpen(false)
  }

  const value: ProducerAuthContextType = {
    isAuthenticated,
    producer,
    isLoading,
    isLoginModalOpen,
    isRegisterModalOpen,
    login,
    register,
    logout,
    showLoginModal,
    showRegisterModal,
    hideModals
  }

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <ProducerAuthContext.Provider value={value}>
        <div style={{ visibility: "hidden" }}>{children}</div>
      </ProducerAuthContext.Provider>
    )
  }

  return (
    <ProducerAuthContext.Provider value={value}>
      {children}

      {/* Producer Login Modal */}
      {isLoginModalOpen && (
        <ProducerLoginModal
          isOpen={isLoginModalOpen}
          onClose={hideModals}
          onLogin={login}
          onSwitchToRegister={showRegisterModal}
        />
      )}

      {/* Producer Register Modal */}
      {isRegisterModalOpen && (
        <ProducerRegistrationModal
          isOpen={isRegisterModalOpen}
          onClose={hideModals}
          onRegister={register}
          onSwitchToLogin={showLoginModal}
        />
      )}
    </ProducerAuthContext.Provider>
  )
}

export function useProducerAuth() {
  const context = React.useContext(ProducerAuthContext)
  if (context === undefined) {
    throw new Error('useProducerAuth must be used within a ProducerAuthProvider')
  }
  return context
}

// Producer Login Modal Component
interface ProducerLoginModalProps {
  isOpen: boolean
  onClose: () => void
  onLogin: (email: string, password: string) => Promise<boolean>
  onSwitchToRegister: () => void
}

function ProducerLoginModal({ isOpen, onClose, onLogin, onSwitchToRegister }: ProducerLoginModalProps) {
  const [email, setEmail] = React.useState('')
  const [password, setPassword] = React.useState('')
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const success = await onLogin(email, password)
      if (!success) {
        setError('Geçersiz e-posta veya şifre')
      }
    } catch (error) {
      setError('Giriş yapılırken bir hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Üretici Giriş</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              E-posta
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Şifre
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              required
            />
          </div>

          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 disabled:opacity-50"
            >
              {isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap'}
            </button>
          </div>
        </form>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            Henüz hesabınız yok mu?{' '}
            <button
              onClick={onSwitchToRegister}
              className="text-amber-600 hover:text-amber-700 font-medium"
            >
              Üretici Üye Ol
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}


