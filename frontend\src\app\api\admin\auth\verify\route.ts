import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const adminToken = cookieStore.get('admin_token');

    if (!adminToken) {
      return NextResponse.json(
        { error: 'No admin token found' },
        { status: 401 }
      );
    }

    // Basit token kontrolü - gerçek uygulamada JWT verify edilecek
    if (adminToken.value === 'admin_authenticated_token') {
      return NextResponse.json({
        success: true,
        admin: {
          id: 'admin_1',
          email: '<EMAIL>',
          name: 'Admin User'
        }
      });
    }

    return NextResponse.json(
      { error: 'Invalid admin token' },
      { status: 401 }
    );
  } catch (error) {
    console.error('Admin auth verify error:', error);
    return NextResponse.json(
      { error: 'Authentication verification failed' },
      { status: 500 }
    );
  }
}
