import { NextRequest, NextResponse } from 'next/server';

// Individual Creative API endpoints

interface AdCreative {
  id: string;
  campaignId: string;
  type: 'text' | 'image' | 'video' | 'carousel';
  headline: string;
  description: string;
  imageUrl?: string;
  performance: {
    impressions: number;
    clicks: number;
    ctr: number;
  };
  aiGenerated: boolean;
}

// Mock creatives storage (gerçek implementasyonda database kullanılacak)
let creatives: AdCreative[] = [
  {
    id: '1',
    campaignId: '1',
    type: 'text',
    headline: 'Premium Traverten Ürünleri',
    description: 'Türkiye\'nin en kaliteli traverten ürünleri ile projelerinizi tamamlayın.',
    performance: {
      impressions: 15230,
      clicks: 405,
      ctr: 2.66
    },
    aiGenerated: true
  },
  {
    id: '2',
    campaignId: '2',
    type: 'image',
    headline: '<PERSON>üks Mermer Koleksiyonu',
    description: 'Evinizi saray haline getirin. Premium mermer ürünlerimizi keşfedin.',
    imageUrl: '/images/marble-collection.jpg',
    performance: {
      impressions: 29450,
      clicks: 834,
      ctr: 2.83
    },
    aiGenerated: true
  }
];

// GET - Fetch specific creative
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const creative = creatives.find(c => c.id === params.id);
    
    if (!creative) {
      return NextResponse.json(
        { success: false, error: 'Creative not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: creative,
      message: 'Creative fetched successfully'
    });
  } catch (error) {
    console.error('Error fetching creative:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch creative' },
      { status: 500 }
    );
  }
}

// PUT - Update creative
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const creativeIndex = creatives.findIndex(c => c.id === params.id);
    
    if (creativeIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Creative not found' },
        { status: 404 }
      );
    }

    // Update creative
    creatives[creativeIndex] = {
      ...creatives[creativeIndex],
      ...body
    };

    return NextResponse.json({
      success: true,
      data: creatives[creativeIndex],
      message: 'Creative updated successfully'
    });
  } catch (error) {
    console.error('Error updating creative:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update creative' },
      { status: 500 }
    );
  }
}

// DELETE - Delete creative
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const creativeIndex = creatives.findIndex(c => c.id === params.id);
    
    if (creativeIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Creative not found' },
        { status: 404 }
      );
    }

    creatives.splice(creativeIndex, 1);

    return NextResponse.json({
      success: true,
      message: 'Creative deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting creative:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete creative' },
      { status: 500 }
    );
  }
}
