// RFC-501: Admin Layout Component
'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import {
  LayoutDashboard,
  Users,
  ShoppingCart,
  CreditCard,
  UserCheck,
  Settings,
  FileText,
  Activity,
  LogOut,
  Bell,
  Package,
  MessageSquare,
  Bot,
  TestTube,
  ChevronRight,
  Factory,
  Building2
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: any;
  description: string;
  badge?: number;
  submenu?: { name: string; href: string; }[];
}

const navigation: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
    description: '<PERSON><PERSON> bakış ve metrikler'
  },
  {
    name: '<PERSON>r<PERSON><PERSON>',
    href: '/admin/product-approvals',
    icon: User<PERSON><PERSON><PERSON>,
    description: 'Bekleyen ürün onaylarını yönet'
  },
  {
    name: 'Ürün Yönetimi',
    href: '/admin/products',
    icon: Package,
    description: 'Tüm ürünleri ve üreticileri yönet'
  },
  {
    name: 'Müşteri Yönetimi',
    href: '/admin/customers',
    icon: Users,
    description: 'Müşteri profillerini ve siparişlerini yönet'
  },
  {
    name: 'Üretici Yönetimi',
    href: '/admin/producers',
    icon: Factory,
    description: 'Üretici profillerini ve performanslarını yönet'
  },
  {
    name: 'Ocak Onayları',
    href: '/admin/quarries',
    icon: Building2,
    description: 'Üretici ocak başvurularını incele ve onayla'
  },
  {
    name: 'Güncel Talepler',
    href: '/admin/quote-requests',
    icon: MessageSquare,
    description: 'Tüm teklif taleplerini ve süreçlerini izle'
  },
  {
    name: 'Numune Yönetimi',
    href: '/admin/sample-requests',
    icon: TestTube,
    description: 'Numune taleplerini onaylayın ve takip edin',
    submenu: [
      { name: 'Tümü', href: '/admin/sample-requests' },
      { name: 'Bekleyen', href: '/admin/sample-requests/pending' },
      { name: 'Onaylanan', href: '/admin/sample-requests/approved' },
      { name: 'Reddedilen', href: '/admin/sample-requests/rejected' },
      { name: 'Gönderilen', href: '/admin/sample-requests/shipped' }
    ]
  },
  {
    name: 'Sipariş Yönetimi',
    href: '/admin/orders',
    icon: ShoppingCart,
    description: 'Siparişleri izle ve yönet'
  },
  {
    name: 'Ödeme Yönetimi',
    href: '/admin/payments',
    icon: CreditCard,
    description: 'Ödemeleri ve escrow\'u yönet'
  },
  {
    name: 'Üretici Onayları',
    href: '/admin/approvals',
    icon: UserCheck,
    description: 'Üretici başvurularını incele',
    badge: 5 // Bu API'den gelecek
  },
  {
    name: 'Sistem İzleme',
    href: '/admin/system',
    icon: Activity,
    description: 'Sistem sağlığı ve performans',
    submenu: [
      { name: 'Genel Bakış', href: '/admin/system' },
      { name: 'Sunucu Durumu', href: '/admin/system/server' },
      { name: 'Veritabanı', href: '/admin/system/database' },
      { name: 'API İzleme', href: '/admin/system/api' },
      { name: 'Loglar', href: '/admin/system/logs' }
    ]
  },
  {
    name: 'AI Pazarlama',
    href: '/admin/ai-marketing',
    icon: Bot,
    description: 'AI destekli dijital pazarlama yönetimi'
  },
  {
    name: 'Raporlar',
    href: '/admin/reports',
    icon: FileText,
    description: 'İş raporları oluştur',
    submenu: [
      { name: 'Genel Bakış', href: '/admin/reports' },
      { name: 'Finansal', href: '/admin/reports/financial' },
      { name: 'İş Performansı', href: '/admin/reports/business' },
      { name: 'Kullanıcılar', href: '/admin/reports/users' },
      { name: 'Sistem', href: '/admin/reports/system' }
    ]
  },
  {
    name: 'Ayarlar',
    href: '/admin/settings',
    icon: Settings,
    description: 'Platform yapılandırması',
    submenu: [
      { name: 'Genel', href: '/admin/settings' },
      { name: 'İletişim', href: '/admin/settings/contact' },
      { name: 'WhatsApp', href: '/admin/settings/whatsapp' },
      { name: 'Email', href: '/admin/settings/email' },
      { name: 'Güvenlik', href: '/admin/settings/security' }
    ]
  }
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authLoading, setAuthLoading] = useState(true);

  console.log('Admin layout rendering for path:', pathname);

  // Admin authentication kontrolü
  React.useEffect(() => {
    // Skip auth check for login pages
    if (pathname === '/admin/login' || pathname === '/admin/forgot-password') {
      setAuthLoading(false);
      return;
    }

    const checkAuth = async () => {
      try {
        console.log('Checking admin auth in layout...');
        const response = await fetch('http://localhost:8000/api/admin/auth/verify', {
          credentials: 'include'
        });

        if (!response.ok) {
          console.log('Auth failed, redirecting to login');
          router.push('/admin/login');
          return;
        }

        const data = await response.json();
        console.log('Auth successful in layout:', data);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Auth check failed in layout:', error);
        router.push('/admin/login');
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuth();
  }, [pathname, router]);

  // Auto-expand menu if current page is in submenu
  React.useEffect(() => {
    // Skip effect for login pages
    if (pathname === '/admin/login' || pathname === '/admin/forgot-password') {
      return;
    }
    navigation.forEach(item => {
      if (item.submenu) {
        const hasActiveSubItem = item.submenu.some(subItem => pathname === subItem.href);
        if (hasActiveSubItem && !expandedMenus.includes(item.name)) {
          setExpandedMenus(prev => [...prev, item.name]);
        }
      }
    });
  }, [pathname]);

  const toggleSubmenu = (menuName: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuName)
        ? prev.filter(name => name !== menuName)
        : [...prev, menuName]
    );
  };

  const isSubmenuExpanded = (menuName: string) => expandedMenus.includes(menuName);

  const isActiveMenu = (item: NavigationItem) => {
    if (pathname === item.href) return true;
    if (item.submenu) {
      return item.submenu.some(subItem => pathname === subItem.href);
    }
    return false;
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
      router.push('/admin/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Admin login ve forgot-password sayfaları için layout'u bypass et
  if (pathname === '/admin/login' || pathname === '/admin/forgot-password') {
    return <>{children}</>;
  }

  // Auth loading durumunda loading göster
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Yetkilendirme kontrol ediliyor...</p>
        </div>
      </div>
    );
  }

  // Auth başarısız ise hiçbir şey gösterme (redirect olacak)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar - Sabit pozisyon */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg flex flex-col">
        {/* Logo */}
        <div className="flex h-16 items-center justify-center border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">MP</span>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">Yönetici Paneli</h1>
              <p className="text-xs text-gray-500">Mermer Pazaryeri</p>
            </div>
          </div>
        </div>

        {/* Navigation - Scrollable area */}
        <div className="flex-1 overflow-y-auto">
          <nav className="mt-6 px-3 pb-6">
            <motion.div layout className="space-y-2">
              {navigation.map((item) => {
                const isActive = isActiveMenu(item);
                const hasSubmenu = item.submenu && item.submenu.length > 0;
                const isExpanded = isSubmenuExpanded(item.name);

                return (
                  <motion.div
                    key={item.name}
                    layout
                    className="mb-1"
                    transition={{ duration: 0.2, ease: 'easeInOut' }}
                  >
                    {/* Main Menu Item */}
                    {hasSubmenu ? (
                      <button
                        onClick={() => toggleSubmenu(item.name)}
                        className={cn(
                          'group flex items-center w-full px-3 py-2 text-sm font-medium rounded-md transition-colors',
                          isActive
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        )}
                      >
                        <item.icon
                          className={cn(
                            'mr-3 h-5 w-5 flex-shrink-0',
                            isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-500'
                          )}
                        />
                        <span className="flex-1 text-left">{item.name}</span>
                        {item.badge && item.badge > 0 && (
                          <Badge variant="destructive" className="ml-2 text-xs">
                            {item.badge}
                          </Badge>
                        )}
                        <motion.div
                          animate={{ rotate: isExpanded ? 90 : 0 }}
                          transition={{ duration: 0.2 }}
                          className="ml-2"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </motion.div>
                      </button>
                    ) : (
                      <Link
                        href={item.href}
                        className={cn(
                          'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                          isActive
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        )}
                      >
                        <item.icon
                          className={cn(
                            'mr-3 h-5 w-5 flex-shrink-0',
                            isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-500'
                          )}
                        />
                        <span className="flex-1">{item.name}</span>
                        {item.badge && item.badge > 0 && (
                          <Badge variant="destructive" className="ml-2 text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </Link>
                    )}

                    {/* Submenu */}
                    {hasSubmenu && isExpanded && (
                      <motion.div
                        layout
                        initial={{ opacity: 0, scaleY: 0 }}
                        animate={{
                          opacity: 1,
                          scaleY: 1,
                          transition: {
                            duration: 0.2,
                            ease: 'easeInOut'
                          }
                        }}
                        exit={{
                          opacity: 0,
                          scaleY: 0,
                          transition: {
                            duration: 0.15
                          }
                        }}
                        className="origin-top"
                      >
                        <div className="ml-6 mt-2 mb-2 space-y-1">
                          {item.submenu!.map((subItem, index) => {
                            const isSubActive = pathname === subItem.href;
                            return (
                              <motion.div
                                key={subItem.name}
                                layout
                                initial={{ x: -10, opacity: 0 }}
                                animate={{ x: 0, opacity: 1 }}
                                transition={{
                                  delay: index * 0.03,
                                  duration: 0.15,
                                  ease: 'easeOut'
                                }}
                              >
                                <Link
                                  href={subItem.href}
                                  className={cn(
                                    'group flex items-center px-3 py-2 text-sm rounded-md transition-colors block w-full',
                                    isSubActive
                                      ? 'bg-blue-100 text-blue-800 font-medium'
                                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                                  )}
                                >
                                  <span className="w-2 h-2 bg-gray-300 rounded-full mr-3 flex-shrink-0"></span>
                                  <span className="truncate">{subItem.name}</span>
                                </Link>
                              </motion.div>
                            );
                          })}
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                );
              })}
            </motion.div>
          </nav>
        </div>

        {/* System Status - Fixed at bottom */}
        <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Sistem Sağlıklı</span>
            </div>
            <Button variant="ghost" size="sm" onClick={handleLogout}>
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pl-64">
        {/* Top Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
          <div className="flex h-16 items-center justify-between px-8">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-gray-900">
                {navigation.find(item => item.href === pathname)?.name || 'Yönetici Paneli'}
              </h2>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="w-5 h-5" />
                <Badge
                  variant="destructive"
                  className="absolute -top-1 -right-1 w-5 h-5 text-xs flex items-center justify-center p-0"
                >
                  3
                </Badge>
              </Button>

              {/* Admin Profile */}
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-700">A</span>
                </div>
                <div className="text-sm">
                  <p className="font-medium text-gray-900">Admin User</p>
                  <p className="text-gray-500"><EMAIL></p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content - Uygun padding ile */}
        <main className="flex-1 p-8">
          {children}
        </main>
      </div>
    </div>
  );
}


