import { NextRequest, NextResponse } from 'next/server';

// Campaign API endpoints for AI Marketing Ads

interface AdCampaign {
  id: string;
  name: string;
  platform: 'google_ads' | 'facebook_ads' | 'linkedin_ads' | 'twitter_ads';
  campaignType: 'search' | 'display' | 'video' | 'shopping' | 'social';
  status: 'active' | 'paused' | 'ended' | 'draft';
  budget: {
    total: number;
    daily: number;
    spent: number;
    currency: string;
  };
  performance: {
    impressions: number;
    clicks: number;
    conversions: number;
    ctr: number;
    cpc: number;
    cpa: number;
    roas: number;
  };
  targetAudience: {
    locations: string[];
    demographics: string;
    interests: string[];
  };
  startDate: Date;
  endDate?: Date;
  aiManaged: boolean;
  lastOptimized?: Date;
}

// Mock campaigns storage (gerçek implementasyonda database kullanılacak)
let campaigns: AdCampaign[] = [
  {
    id: '1',
    name: 'Traverten Ürünleri - Google Search',
    platform: 'google_ads',
    campaignType: 'search',
    status: 'active',
    budget: {
      total: 5000,
      daily: 100,
      spent: 2340,
      currency: 'USD'
    },
    performance: {
      impressions: 45230,
      clicks: 1205,
      conversions: 34,
      ctr: 2.66,
      cpc: 1.94,
      cpa: 68.82,
      roas: 3.2
    },
    targetAudience: {
      locations: ['US', 'DE', 'IT'],
      demographics: '25-65, All genders',
      interests: ['Construction', 'Architecture', 'Home Improvement']
    },
    startDate: new Date('2025-06-15'),
    endDate: new Date('2025-07-15'),
    aiManaged: true,
    lastOptimized: new Date('2025-07-03')
  }
];

// GET - Fetch all campaigns
export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      data: campaigns,
      message: 'Campaigns fetched successfully'
    });
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
}

// POST - Create new campaign
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newCampaign: AdCampaign = {
      id: Date.now().toString(),
      name: body.name,
      platform: body.platform,
      campaignType: body.campaignType,
      status: body.status || 'draft',
      budget: {
        total: body.totalBudget,
        daily: body.dailyBudget,
        spent: 0,
        currency: 'USD'
      },
      performance: {
        impressions: 0,
        clicks: 0,
        conversions: 0,
        ctr: 0,
        cpc: 0,
        cpa: 0,
        roas: 0
      },
      targetAudience: {
        locations: body.targetLocations ? body.targetLocations.split(',').map((l: string) => l.trim()) : [],
        demographics: body.targetDemographics || '',
        interests: body.targetInterests ? body.targetInterests.split(',').map((i: string) => i.trim()) : []
      },
      startDate: new Date(),
      aiManaged: body.aiManaged || false
    };

    campaigns.push(newCampaign);

    return NextResponse.json({
      success: true,
      data: newCampaign,
      message: 'Campaign created successfully'
    });
  } catch (error) {
    console.error('Error creating campaign:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create campaign' },
      { status: 500 }
    );
  }
}
